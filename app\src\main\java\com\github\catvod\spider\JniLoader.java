package com.github.catvod.spider;

import android.content.Context;
import android.os.Build;
import android.util.Log;

import com.github.catvod.crawler.SpiderDebug;
import com.github.catvod.utils.Notify;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;

/**
 * JNI动态加载器
 * 用于从assets/libs/目录中动态加载libgojni.so文件并调用其中的方法
 */
public class JniLoader {
    
    private static final String TAG = "JniLoader";
    private static final String LIB_NAME = "libgojni.so";
    private static boolean isLibraryLoaded = false;
    
    // 声明native方法
    public static native int startProxyServer(int port);
    public static native void stopProxyServer();
    public static native boolean isProxyServerRunning();
    
    /**
     * 动态加载libgojni.so库
     * @param context 应用上下文
     * @return 是否加载成功
     */
    public static synchronized boolean loadLibrary(Context context) {
        if (isLibraryLoaded) {
            SpiderDebug.log("libgojni.so 已经加载过了");
            return true;
        }
        
        try {
            // 获取当前设备的ABI
            String abi = getCurrentAbi();
            SpiderDebug.log("当前设备ABI: " + abi);
            
            // 从assets中复制so文件到应用私有目录
            File soFile = extractSoFile(context, abi);
            if (soFile == null || !soFile.exists()) {
                SpiderDebug.log("提取so文件失败");
                return false;
            }
            
            // 加载so文件
            System.load(soFile.getAbsolutePath());
            isLibraryLoaded = true;
            
            SpiderDebug.log("libgojni.so 加载成功: " + soFile.getAbsolutePath());
            return true;
            
        } catch (Exception e) {
            SpiderDebug.log("加载libgojni.so失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 获取当前设备的ABI
     */
    private static String getCurrentAbi() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            String[] abis = Build.SUPPORTED_ABIS;
            if (abis.length > 0) {
                return abis[0];
            }
        } else {
            return Build.CPU_ABI;
        }
        return "unknown";
    }
    
    /**
     * 从assets中提取so文件到应用私有目录
     */
    private static File extractSoFile(Context context, String abi) {
        try {
            // 确定assets中的so文件路径
            String assetPath = "libs/" + abi + "/" + LIB_NAME;
            SpiderDebug.log("尝试从assets加载: " + assetPath);
            
            // 打开assets中的so文件
            InputStream inputStream = context.getAssets().open(assetPath);
            
            // 创建目标文件
            File libDir = new File(context.getFilesDir(), "libs");
            if (!libDir.exists()) {
                libDir.mkdirs();
            }
            
            File soFile = new File(libDir, LIB_NAME);
            
            // 如果文件已存在且大小相同，直接返回
            if (soFile.exists()) {
                long assetSize = getAssetFileSize(context, assetPath);
                if (assetSize > 0 && soFile.length() == assetSize) {
                    SpiderDebug.log("so文件已存在且大小匹配，直接使用: " + soFile.getAbsolutePath());
                    inputStream.close();
                    return soFile;
                }
            }
            
            // 复制文件
            FileOutputStream outputStream = new FileOutputStream(soFile);
            byte[] buffer = new byte[8192];
            int bytesRead;
            
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            
            inputStream.close();
            outputStream.close();
            
            // 设置文件权限
            soFile.setExecutable(true);
            soFile.setReadable(true);
            
            SpiderDebug.log("so文件提取成功: " + soFile.getAbsolutePath() + ", 大小: " + soFile.length());
            return soFile;
            
        } catch (Exception e) {
            SpiderDebug.log("提取so文件失败: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    
    /**
     * 获取assets文件大小
     */
    private static long getAssetFileSize(Context context, String assetPath) {
        try {
            InputStream inputStream = context.getAssets().open(assetPath);
            long size = inputStream.available();
            inputStream.close();
            return size;
        } catch (Exception e) {
            return -1;
        }
    }
    
    /**
     * 启动代理服务器
     * @param port 端口号
     * @return 启动结果码
     */
    public static int startProxy(int port) {
        if (!isLibraryLoaded) {
            SpiderDebug.log("libgojni.so 未加载，无法启动代理服务器");
            return -1;
        }
        
        try {
            int result = startProxyServer(port);
            SpiderDebug.log("代理服务器启动结果: " + result + ", 端口: " + port);
            return result;
        } catch (Exception e) {
            SpiderDebug.log("启动代理服务器失败: " + e.getMessage());
            e.printStackTrace();
            return -1;
        }
    }
    
    /**
     * 停止代理服务器
     */
    public static void stopProxy() {
        if (!isLibraryLoaded) {
            SpiderDebug.log("libgojni.so 未加载，无法停止代理服务器");
            return;
        }
        
        try {
            stopProxyServer();
            SpiderDebug.log("代理服务器已停止");
        } catch (Exception e) {
            SpiderDebug.log("停止代理服务器失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 检查代理服务器是否运行
     */
    public static boolean isProxyRunning() {
        if (!isLibraryLoaded) {
            return false;
        }
        
        try {
            return isProxyServerRunning();
        } catch (Exception e) {
            SpiderDebug.log("检查代理服务器状态失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取库加载状态
     */
    public static boolean isLoaded() {
        return isLibraryLoaded;
    }
}
